import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:video_player/video_player.dart';
import 'providers/user_provider.dart';
import 'providers/api_cache_provider.dart';
import 'providers/locale_provider.dart';
import 'core/state/app_state_manager.dart';
import 'pages/main/main_page.dart';
import 'pages/auth/login_page.dart';
import 'utils/background_loader.dart';
import 'services/auth_service.dart';
import 'constants/fonts.dart'; // 导入字体常量类
import 'core/routing/app_router.dart'; // 导入统一路由管理
import 'core/utils/performance_monitor.dart';
import 'core/routing/lazy_route_manager.dart';
import 'shared/widgets/optimized_image.dart';

// 初始化函数确保应用启动前需要的服务已经准备好
Future<void> initApp() async {
  // 初始化性能监控
  PerformanceMonitor().startMonitoring();

  // 配置图片缓存
  ImageMemoryManager.configureImageCache();

  // 初始化认证服务
  await AuthService.initialize();

  // 检查令牌有效性
  final isTokenValid = await AuthService.checkTokenOnStartup();
  if (!isTokenValid) {
    // 如果令牌无效，清除用户数据
    await AuthService.clearToken();
    print('启动时令牌验证失败，已清除令牌');
  } else {
    print('令牌有效，保持登录状态');
  }

  // 智能预加载常用路由
  IntelligentPreloader.intelligentPreload();
}

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 执行应用初始化
  await initApp();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => UserProvider()),
        ChangeNotifierProvider(create: (context) => ApiCacheProvider()),
        ChangeNotifierProvider(create: (context) => LocaleProvider()..init()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    // 应用首次构建完成后，在后台预加载常用数据
    BackgroundLoader.preloadCommonData(context);

    return CupertinoApp(
      title: 'WakeUP',
      debugShowCheckedModeBanner: false,
      // 添加本地化支持
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        DefaultCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('en'), // English
        Locale('zh'), // Chinese
      ],
      locale: localeProvider.locale,
      localeResolutionCallback: (locale, supportedLocales) {
        // 如果设备语言在支持列表中，使用设备语言
        for (var supportedLocale in supportedLocales) {
          if (locale != null &&
              locale.languageCode == supportedLocale.languageCode) {
            return supportedLocale;
          }
        }
        // 默认使用英文
        return const Locale('en');
      },
      theme: CupertinoThemeData(
        primaryColor: CupertinoColors.white,
        brightness: Brightness.dark,
        scaffoldBackgroundColor: CupertinoColors.black,
        barBackgroundColor: const Color(0xFF111111),
        textTheme: CupertinoTextThemeData(
          navTitleTextStyle: TextStyle(
            fontFamily: AppFonts.platformChineseFont, // 使用平台自适应中文字体
            color: CupertinoColors.white,
            fontSize: 17.0,
            fontWeight: FontWeight.w600,
            fontFamilyFallback: [AppFonts.platformLatinFont], // 添加英文字体回退
          ),
          textStyle: TextStyle(
            fontFamily: AppFonts.platformChineseFont, // 使用平台自适应中文字体
            color: CupertinoColors.white,
            fontFamilyFallback: [AppFonts.platformLatinFont], // 添加英文字体回退
          ),
          actionTextStyle: TextStyle(
            fontFamily: AppFonts.platformChineseFont, // 使用平台自适应中文字体
            color: CupertinoColors.white,
            fontWeight: FontWeight.w600,
            fontFamilyFallback: [AppFonts.platformLatinFont], // 添加英文字体回退
          ),
        ),
      ),
      // 添加Material组件支持
      builder: (context, child) {
        // 提供MaterialLocalizations
        return Material(color: CupertinoColors.black, child: child!);
      },
      home: VideoSplashScreen(
        child:
            userProvider.isLoading
                ? const Center(
                  child: CupertinoActivityIndicator(
                    color: CupertinoColors.white,
                  ),
                )
                : userProvider.isLoggedIn
                ? const MainPage()
                : const LoginPage(),
      ),
      routes: AppRouter.routes,
    );
  }
}

/// 视频启动屏幕组件，播放启动视频并在后台加载数据
class VideoSplashScreen extends StatefulWidget {
  final Widget child;

  const VideoSplashScreen({super.key, required this.child});

  @override
  State<VideoSplashScreen> createState() => _VideoSplashScreenState();
}

class _VideoSplashScreenState extends State<VideoSplashScreen> {
  VideoPlayerController? _controller;
  bool _isReady = false;
  bool _videoLoaded = false;
  bool _showFallback = false;
  bool _hasJumped = false; // 防重复跳转标记
  bool _showBlackTransition = false; // 黑屏过渡状态
  static const Duration _transitionDuration = Duration(
    milliseconds: 800,
  ); // 过渡时长

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      print('开始初始化splash视频...');
      _controller = VideoPlayerController.asset(
        'assets/videos/splash_video.mp4',
      );
      await _controller!.initialize();

      print('视频初始化成功！视频尺寸: ${_controller!.value.size}');

      if (mounted) {
        setState(() {
          _videoLoaded = true;
        });

        // 开始播放视频
        _controller!.play();
        print('开始播放splash视频');

        // 监听视频播放完成
        _controller!.addListener(_videoListener);
      }
    } catch (e) {
      print('视频加载失败: $e');
      if (mounted) {
        setState(() {
          _showFallback = true;
        });
        // 视频加载失败时使用延迟显示
        Future.delayed(const Duration(milliseconds: 2000), () {
          if (mounted && !_hasJumped) {
            print('视频加载失败，开始黑屏过渡');
            _hasJumped = true;
            setState(() {
              _showBlackTransition = true;
            });

            // 延迟后跳转到主应用
            Future.delayed(_transitionDuration, () {
              if (mounted) {
                print('黑屏过渡完成，跳转到应用');
                setState(() {
                  _isReady = true;
                });
              }
            });
          }
        });
      }
    }
  }

  void _videoListener() {
    if (_controller != null && mounted && !_hasJumped) {
      // 检查视频是否播放完成
      if (_controller!.value.position >= _controller!.value.duration &&
          _controller!.value.duration > Duration.zero) {
        print('视频播放完成，开始黑屏过渡');
        _hasJumped = true; // 标记已跳转，防止重复
        setState(() {
          _showBlackTransition = true;
        });

        // 延迟后跳转到主应用
        Future.delayed(_transitionDuration, () {
          if (mounted) {
            print('黑屏过渡完成，跳转到应用');
            setState(() {
              _isReady = true;
            });
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _controller?.removeListener(_videoListener);
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isReady) {
      return widget.child;
    }

    // 显示黑屏过渡
    if (_showBlackTransition) {
      return _buildBlackTransition(context);
    }

    // 如果视频加载失败，显示备用启动屏幕
    if (_showFallback) {
      return _buildFallbackSplash(context);
    }

    // 视频启动屏幕
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.black,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 视频播放器 - 全屏适配
          if (_videoLoaded && _controller != null)
            SizedBox.expand(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: _controller!.value.size.width,
                  height: _controller!.value.size.height,
                  child: VideoPlayer(_controller!),
                ),
              ),
            ),

          // 加载状态
          if (!_videoLoaded) _buildFallbackSplash(context),
        ],
      ),
    );
  }

  Widget _buildBlackTransition(BuildContext context) {
    // 纯黑色过渡页面，带有淡入效果
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.black,
      child: AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 200),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: CupertinoColors.black,
        ),
      ),
    );
  }

  Widget _buildFallbackSplash(BuildContext context) {
    // 返回纯黑色背景，无任何UI元素
    return Container(color: CupertinoColors.black);
  }
}
