import 'dart:ui';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Elegant Glass Card - Static glowing glass effect
/// Inspired by the reference image with beautiful static glow and transparency
class ElegantGlassCard extends StatefulWidget {
  final Widget child;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? glowColor;
  final double glowIntensity;
  final double transparency;
  final bool enableTouchResponse;

  const ElegantGlassCard({
    super.key,
    required this.child,
    this.borderRadius,
    this.padding,
    this.margin,
    this.glowColor,
    this.glowIntensity = 0.6,
    this.transparency = 0.15,
    this.enableTouchResponse = true,
  });

  @override
  State<ElegantGlassCard> createState() => _ElegantGlassCardState();
}

class _ElegantGlassCardState extends State<ElegantGlassCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _touchController;
  late Animation<double> _touchAnimation;

  bool _isTouched = false;
  Offset? _touchPosition;

  @override
  void initState() {
    super.initState();

    _touchController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _touchAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _touchController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _touchController.dispose();
    super.dispose();
  }

  void _handleTouchStart(Offset position) {
    if (!widget.enableTouchResponse) return;

    setState(() {
      _isTouched = true;
      _touchPosition = position;
    });

    _touchController.forward();
    HapticFeedback.lightImpact();
  }

  void _handleTouchEnd() {
    if (!widget.enableTouchResponse) return;

    setState(() {
      _isTouched = false;
    });

    _touchController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      child: GestureDetector(
        onTapDown: (details) => _handleTouchStart(details.localPosition),
        onTapUp: (_) => _handleTouchEnd(),
        onTapCancel: _handleTouchEnd,
        onPanStart: (details) => _handleTouchStart(details.localPosition),
        onPanEnd: (_) => _handleTouchEnd(),
        child: AnimatedBuilder(
          animation: _touchAnimation,
          builder: (context, child) {
            return Stack(
              children: [
                // Outer glow effect
                _buildOuterGlow(),

                // Main glass container
                _buildGlassContainer(),

                // Touch ripple effect
                if (widget.enableTouchResponse &&
                    _isTouched &&
                    _touchPosition != null)
                  _buildTouchRipple(),

                // Content
                Container(padding: widget.padding, child: widget.child),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildOuterGlow() {
    final glowColor =
        widget.glowColor ??
        const Color(0xFF8B5CF6); // Purple glow like in the image
    final glowRadius = 20.0 + (_touchAnimation.value * 10.0);

    return Container(
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
        boxShadow: [
          // Main outer glow
          BoxShadow(
            color: glowColor.withOpacity(widget.glowIntensity * 0.4),
            blurRadius: glowRadius,
            spreadRadius: 4,
            offset: const Offset(0, 0),
          ),
          // Secondary glow for depth
          BoxShadow(
            color: glowColor.withOpacity(widget.glowIntensity * 0.2),
            blurRadius: glowRadius * 1.5,
            spreadRadius: 8,
            offset: const Offset(0, 4),
          ),
          // Inner highlight
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 2,
            spreadRadius: 0.5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
    );
  }

  Widget _buildGlassContainer() {
    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          decoration: BoxDecoration(
            // Glass material effect
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(widget.transparency + 0.05),
                Colors.white.withOpacity(widget.transparency - 0.05),
                Colors.white.withOpacity(widget.transparency - 0.02),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 1.0,
            ),
          ),
          child: Container(
            decoration: BoxDecoration(
              // Inner subtle gradient for depth
              gradient: RadialGradient(
                center: const Alignment(0.3, -0.3),
                radius: 1.0,
                colors: [
                  Colors.white.withOpacity(0.15),
                  Colors.white.withOpacity(0.05),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.4, 1.0],
              ),
              borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTouchRipple() {
    if (_touchPosition == null) return const SizedBox.shrink();

    final rippleRadius = 60.0 * _touchAnimation.value;
    final rippleOpacity = (1.0 - _touchAnimation.value) * 0.3;

    return Positioned.fill(
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.circular(24),
        child: CustomPaint(
          painter: TouchRipplePainter(
            touchPosition: _touchPosition!,
            radius: rippleRadius,
            opacity: rippleOpacity,
            color: widget.glowColor ?? const Color(0xFF8B5CF6),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for touch ripple effect
class TouchRipplePainter extends CustomPainter {
  final Offset touchPosition;
  final double radius;
  final double opacity;
  final Color color;

  TouchRipplePainter({
    required this.touchPosition,
    required this.radius,
    required this.opacity,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (opacity <= 0) return;

    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.screen;

    final gradient = RadialGradient(
      colors: [
        color.withOpacity(opacity),
        color.withOpacity(opacity * 0.5),
        Colors.transparent,
      ],
      stops: const [0.0, 0.6, 1.0],
    );

    paint.shader = gradient.createShader(
      Rect.fromCircle(center: touchPosition, radius: radius),
    );

    canvas.drawCircle(touchPosition, radius, paint);

    // Add inner bright point
    final brightPaint =
        Paint()
          ..color = Colors.white.withOpacity(opacity * 0.8)
          ..style = PaintingStyle.fill
          ..blendMode = BlendMode.screen;

    canvas.drawCircle(touchPosition, radius * 0.2, brightPaint);
  }

  @override
  bool shouldRepaint(covariant TouchRipplePainter oldDelegate) {
    return oldDelegate.touchPosition != touchPosition ||
        oldDelegate.radius != radius ||
        oldDelegate.opacity != opacity;
  }
}

/// Elegant Glass Card with System Information Layout
/// Specifically designed for system info cards like in the reference image
class SystemInfoGlassCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final List<InfoRow> infoRows;
  final VoidCallback? onMoreInfo;
  final Color? glowColor;

  const SystemInfoGlassCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.infoRows,
    this.onMoreInfo,
    this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    return ElegantGlassCard(
      glowColor: glowColor ?? const Color(0xFF8B5CF6),
      glowIntensity: 0.7,
      transparency: 0.12,
      borderRadius: BorderRadius.circular(28),
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Icon(icon, size: 40, color: Colors.white.withOpacity(0.9)),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // Subtitle
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Info rows
          ...infoRows.map(
            (row) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    row.label,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    row.value,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (onMoreInfo != null) ...[
            const SizedBox(height: 32),

            // More Info Button
            GestureDetector(
              onTap: onMoreInfo,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: (glowColor ?? const Color(0xFF8B5CF6)).withOpacity(
                    0.2,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: (glowColor ?? const Color(0xFF8B5CF6)).withOpacity(
                      0.3,
                    ),
                    width: 1,
                  ),
                ),
                child: const Text(
                  'More Info',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class InfoRow {
  final String label;
  final String value;

  const InfoRow({required this.label, required this.value});
}
