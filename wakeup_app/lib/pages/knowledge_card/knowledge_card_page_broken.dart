import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../models/knowledge_card_model.dart';
import '../../providers/user_provider.dart';
import '../../services/knowledge_card_service.dart';
import '../../widgets/elegant_glass_card.dart';

class KnowledgeCardPage extends StatefulWidget {
  const KnowledgeCardPage({super.key});

  @override
  State<KnowledgeCardPage> createState() => _KnowledgeCardPageState();
}

class _KnowledgeCardPageState extends State<KnowledgeCardPage>
    with TickerProviderStateMixin {
  // PageView控制器
  PageController _pageController = PageController();

  // 动画控制器
  late AnimationController _flipController;
  late Animation<double> _flipAnimation;

  // 数据状态
  List<KnowledgeCard> _knowledgeCards = [];
  bool _isLoading = true;
  bool _isFlipped = false;
  int _currentIndex = 0;
  int _currentQuestionIndex = 0;

  // 知识卡服务
  final KnowledgeCardService _knowledgeCardService = KnowledgeCardService();

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _flipAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _flipController, curve: Curves.easeInOut),
    );

    _loadKnowledgeCards();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _flipController.dispose();
    super.dispose();
  }

  Future<void> _loadKnowledgeCards() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final cards = await _knowledgeCardService.getKnowledgeCards(
        userProvider.userId.toString(),
      );

      setState(() {
        _knowledgeCards = cards;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // 显示错误提示
      if (mounted) {
        _showErrorDialog('加载知识卡失败：$e');
      }
    }
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: const Text('错误'),
            content: Text(message),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('确定'),
              ),
            ],
          ),
    );
  }

  // 翻转卡片
  void _flipCard() {
    HapticFeedback.mediumImpact();

    if (_isFlipped) {
      _flipController.reverse();
    } else {
      _flipController.forward();
    }

    setState(() {
      _isFlipped = !_isFlipped;
      if (_isFlipped) {
        _currentQuestionIndex = 0;
      }
    });
  }

  // 处理垂直滑动
  void _handleVerticalSwipe(DragUpdateDetails details) {
    // 向下滑动切换到下一个知识卡
    if (details.delta.dy > 10 && _currentIndex < _knowledgeCards.length - 1) {
      _nextCard();
    }
    // 向上滑动切换到上一个知识卡
    else if (details.delta.dy < -10 && _currentIndex > 0) {
      _previousCard();
    }
  }

  // 下一个知识卡
  void _nextCard() {
    if (_currentIndex < _knowledgeCards.length - 1) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentIndex++;
        _isFlipped = false;
        _currentQuestionIndex = 0;
      });
      _flipController.reset();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // 上一个知识卡
  void _previousCard() {
    if (_currentIndex > 0) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentIndex--;
        _isFlipped = false;
        _currentQuestionIndex = 0;
      });
      _flipController.reset();
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // 下一个题目
  void _nextQuestion() {
    final currentCard = _knowledgeCards[_currentIndex];
    if (_currentQuestionIndex < currentCard.relatedQuestions.length - 1) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentQuestionIndex++;
      });
    }
  }

  // 上一个题目
  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      HapticFeedback.selectionClick();
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 渐变背景
          Container(
            decoration: const BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topRight,
                radius: 1.5,
                colors: [
                  Color(0xFF6A5ACD),
                  Color(0xFF4B0082),
                  Color(0xFF000000),
                ],
                stops: [0.0, 0.6, 1.0],
              ),
            ),
          ),

          // 背景装饰圆圈
          ..._buildBackgroundDecorations(),

          // 主要内容
          SafeArea(
            child:
                _isLoading
                    ? const Center(
                      child: CupertinoActivityIndicator(
                        color: Colors.white,
                        radius: 16,
                      ),
                    )
                    : _knowledgeCards.isEmpty
                    ? _buildEmptyState()
                    : _buildKnowledgeCardsView(),
          ),
        ],
      ),
    );
  }

  // 构建背景装饰
  List<Widget> _buildBackgroundDecorations() {
    return [
      Positioned(
        top: -50,
        right: -50,
        child: Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.purple.withOpacity(0.3), Colors.transparent],
            ),
          ),
        ),
      ),
      Positioned(
        bottom: -80,
        left: -80,
        child: Container(
          width: 250,
          height: 250,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.blue.withOpacity(0.2), Colors.transparent],
            ),
          ),
        ),
      ),
    ];
  }

  // 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 0.5,
              ),
            ),
            child: Icon(
              CupertinoIcons.collections,
              size: 48,
              color: Colors.white.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            '暂无知识卡',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '请先在选题中加入课程',
            style: TextStyle(
              color: Colors.white.withOpacity(0.5),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // 构建知识卡视图
  Widget _buildKnowledgeCardsView() {
    return GestureDetector(
      onPanUpdate: _handleVerticalSwipe,
      child: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        itemCount: _knowledgeCards.length,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
            _isFlipped = false;
            _currentQuestionIndex = 0;
          });
          _flipController.reset();
        },
        itemBuilder: (context, index) {
          final card = _knowledgeCards[index];
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
            child: AnimatedBuilder(
              animation: _flipAnimation,
              builder: (context, child) {
                final isShowingFront = _flipAnimation.value < 0.5;
                return Transform(
                  alignment: Alignment.center,
                  transform:
                      Matrix4.identity()
                        ..setEntry(3, 2, 0.001)
                        ..rotateY(_flipAnimation.value * 3.14159),
                  child:
                      isShowingFront
                          ? _buildKnowledgeCardFront(card)
                          : Transform(
                            alignment: Alignment.center,
                            transform: Matrix4.identity()..rotateY(3.14159),
                            child: _buildKnowledgeCardBack(card),
                          ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  // 构建知识卡正面
  Widget _buildKnowledgeCardFront(KnowledgeCard card) {
    return ElegantGlassCard(
      glowColor: const Color(0xFF8B5CF6), // Purple glow like in reference
      glowIntensity: 0.6,
      transparency: 0.15,
      borderRadius: BorderRadius.circular(24),
      padding: const EdgeInsets.all(32),
      child: GestureDetector(
        onTap: _flipCard,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题区域
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 0.5,
                      ),
                    ),
                    child: Text(
                      card.courseName,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${_currentIndex + 1} / ${_knowledgeCards.length}',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // 主标题
              Text(
                card.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  height: 1.3,
                ),
              ),

              const SizedBox(height: 24),

                             // 内容
               Expanded(
                 child: SingleChildScrollView(
                   child: Text(
                     card.content,
                     style: TextStyle(
                       color: Colors.white.withOpacity(0.85),
                       fontSize: 16,
                       height: 1.6,
                       letterSpacing: 0.3,
                     ),
                   ),
                 ),
               ),

              // 关键点
              if (card.keyPoints.isNotEmpty) ...[
                const SizedBox(height: 24),
                Text(
                  '关键要点',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                ...card.keyPoints.map(
                  (point) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 8, right: 12),
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.6),
                            shape: BoxShape.circle,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            point,
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.8),
                              fontSize: 14,
                              height: 1.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              // 底部提示
              const SizedBox(height: 32),
              Center(
                child: Column(
                  children: [
                    Icon(
                      CupertinoIcons.hand_point_left_fill,
                      color: Colors.white.withOpacity(0.4),
                      size: 20,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '点击翻转查看相关题目',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.5),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建知识卡背面
  Widget _buildKnowledgeCardBack(KnowledgeCard card) {
    if (card.relatedQuestions.isEmpty) {
      return ElegantGlassCard(
        glowColor: const Color(0xFF4338CA), // Darker blue for back side
        glowIntensity: 0.4,
        transparency: 0.12,
        borderRadius: BorderRadius.circular(24),
        padding: const EdgeInsets.all(32),
        child: Container(
          width: double.infinity,
          height: double.infinity,
          child: const Center(
            child: Text(
              '暂无相关题目',
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
          ),
        ),
      );
    }

    final question = card.relatedQuestions[_currentQuestionIndex];

    return ElegantGlassCard(
      glowColor: const Color(0xFF4338CA), // Darker blue for back side
      glowIntensity: 0.4,
      transparency: 0.12,
      borderRadius: BorderRadius.circular(24),
      padding: const EdgeInsets.all(32),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顶部导航
            Row(
              children: [
                Text(
                  '相关题目',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_currentQuestionIndex + 1} / ${card.relatedQuestions.length}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 14,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 32),

            // 题目
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        question.question,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          height: 1.4,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // 选项
                      ...question.options.asMap().entries.map((entry) {
                        final index = entry.key;
                        final option = entry.value;
                        final isCorrect = option == question.correctAnswer;

                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color:
                                isCorrect
                                    ? Colors.green.withOpacity(0.2)
                                    : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color:
                                  isCorrect
                                      ? Colors.green.withOpacity(0.5)
                                      : Colors.white.withOpacity(0.2),
                              width: 0.5,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 24,
                                height: 24,
                                decoration: BoxDecoration(
                                  color:
                                      isCorrect
                                          ? Colors.green.withOpacity(0.3)
                                          : Colors.white.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 0.5,
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    String.fromCharCode(
                                      65 + index,
                                    ), // A, B, C, D
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  option,
                                  style: TextStyle(
                                    color: Colors.white.withOpacity(0.9),
                                    fontSize: 14,
                                    height: 1.4,
                                  ),
                                ),
                              ),
                              if (isCorrect)
                                Icon(
                                  CupertinoIcons.checkmark_circle_fill,
                                  color: Colors.green.withOpacity(0.8),
                                  size: 16,
                                ),
                            ],
                          ),
                        );
                      }),

                      if (question.explanation.isNotEmpty) ...[
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Colors.blue.withOpacity(0.3),
                              width: 0.5,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '解析',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.9),
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                question.explanation,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 13,
                                  height: 1.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),

              // 底部导航区域
              _buildBottomNavigationSection(card),
            ],
          ),
        ),
      );
  }

  Widget _buildBottomNavigationSection(KnowledgeCard card) {
    return Column(
      children: [
        const SizedBox(height: 24),
        if (card.relatedQuestions.length > 1)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    onPressed:
                        _currentQuestionIndex > 0 ? _previousQuestion : null,
                    padding: EdgeInsets.zero,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            _currentQuestionIndex > 0
                                ? Colors.white.withOpacity(0.1)
                                : Colors.white.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            CupertinoIcons.chevron_left,
                            size: 16,
                            color:
                                _currentQuestionIndex > 0
                                    ? Colors.white.withOpacity(0.8)
                                    : Colors.white.withOpacity(0.3),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '上一题',
                            style: TextStyle(
                              color:
                                  _currentQuestionIndex > 0
                                      ? Colors.white.withOpacity(0.8)
                                      : Colors.white.withOpacity(0.3),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  CupertinoButton(
                    onPressed:
                        _currentQuestionIndex < card.relatedQuestions.length - 1
                            ? _nextQuestion
                            : null,
                    padding: EdgeInsets.zero,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color:
                            _currentQuestionIndex <
                                    card.relatedQuestions.length - 1
                                ? Colors.white.withOpacity(0.1)
                                : Colors.white.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.2),
                          width: 0.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '下一题',
                            style: TextStyle(
                              color:
                                  _currentQuestionIndex <
                                          card.relatedQuestions.length - 1
                                      ? Colors.white.withOpacity(0.8)
                                      : Colors.white.withOpacity(0.3),
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            CupertinoIcons.chevron_right,
                            size: 16,
                            color:
                                _currentQuestionIndex <
                                        card.relatedQuestions.length - 1
                                    ? Colors.white.withOpacity(0.8)
                                    : Colors.white.withOpacity(0.3),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

            // 滑动提示
            const SizedBox(height: 16),
            Center(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        CupertinoIcons.arrow_up,
                        color: Colors.white.withOpacity(0.4),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Icon(
                        CupertinoIcons.arrow_down,
                        color: Colors.white.withOpacity(0.4),
                        size: 16,
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    '上下滑动切换知识卡',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.5),
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
  }
}
