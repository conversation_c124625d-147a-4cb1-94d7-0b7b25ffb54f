import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class TestGlassCardPage extends StatelessWidget {
  const TestGlassCardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: Colors.black,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 深邃夜空背景
          _buildNightSkyBackground(),
          
          // 极光效果
          _buildAuroraEffects(),
          
          // 主要内容
          SafeArea(
            child: _buildGlowingGlassCard(),
          ),
        ],
      ),
    );
  }

  // 构建深邃夜空背景
  Widget _buildNightSkyBackground() {
    return Container(
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment(-0.3, -0.5),
          radius: 1.5,
          colors: [
            Color(0xFF1A0B2E), // 深紫色中心
            Color(0xFF0F0515), // 深紫黑色
            Color(0xFF000000), // 纯黑边缘
          ],
          stops: [0.0, 0.6, 1.0],
        ),
      ),
    );
  }

  // 构建极光效果
  Widget _buildAuroraEffects() {
    return Stack(
      children: [
        // 左上角主要极光
        Positioned(
          top: -200,
          left: -200,
          child: Container(
            width: 500,
            height: 500,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFFE91E63).withValues(alpha: 0.9), // 亮粉色
                  const Color(0xFF9C27B0).withValues(alpha: 0.7), // 紫色
                  const Color(0xFF673AB7).withValues(alpha: 0.5), // 深紫色
                  const Color(0xFF3F51B5).withValues(alpha: 0.3), // 蓝紫色
                  Colors.transparent,
                ],
                stops: const [0.0, 0.2, 0.4, 0.7, 1.0],
              ),
            ),
          ),
        ),
        
        // 右侧辅助极光
        Positioned(
          top: -100,
          right: -150,
          child: Container(
            width: 350,
            height: 350,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFF9C27B0).withValues(alpha: 0.4),
                  const Color(0xFF673AB7).withValues(alpha: 0.3),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建发光玻璃卡片
  Widget _buildGlowingGlassCard() {
    return Center(
      child: Container(
        width: 320,
        height: 420,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24),
          // 真正的玻璃效果 - 透明背景
          color: Colors.white.withValues(alpha: 0.05),
          // 发光边框
          border: Border.all(
            color: const Color(0xFFE91E63).withValues(alpha: 0.8),
            width: 1.5,
          ),
          // 多层阴影创造发光效果
          boxShadow: [
            // 内部发光
            BoxShadow(
              color: const Color(0xFFE91E63).withValues(alpha: 0.3),
              offset: const Offset(0, 0),
              blurRadius: 20,
              spreadRadius: -5,
            ),
            // 外部发光
            BoxShadow(
              color: const Color(0xFF9C27B0).withValues(alpha: 0.4),
              offset: const Offset(0, 0),
              blurRadius: 40,
              spreadRadius: -10,
            ),
            // 底部强烈发光
            BoxShadow(
              color: const Color(0xFFE91E63).withValues(alpha: 0.6),
              offset: const Offset(0, 8),
              blurRadius: 30,
              spreadRadius: -8,
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Finder 图标
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF4FC3F7),
                      Color(0xFF29B6F6),
                      Color(0xFF0288D1),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF0288D1).withValues(alpha: 0.3),
                      offset: const Offset(0, 4),
                      blurRadius: 12,
                    ),
                  ],
                ),
                child: const Icon(
                  CupertinoIcons.folder_fill,
                  size: 40,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // 标题
              const Text(
                'Apple MacBook',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // 副标题
              Text(
                '16 inch, November 2025',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.7),
                  letterSpacing: 0.3,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // 规格信息
              Column(
                children: [
                  _buildSpecRow('Chip', 'Apple M10 Max'),
                  _buildSpecRow('Memory', '256GB'),
                  _buildSpecRow('Startup disk', 'Macintosh HD'),
                  _buildSpecRow('Serial number', 'X0010XUJYSZX'),
                  _buildSpecRow('macOS', 'Sequoia 20.0'),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // More Info 按钮
              Container(
                width: double.infinity,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFF9C27B0),
                      Color(0xFF673AB7),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF9C27B0).withValues(alpha: 0.4),
                      offset: const Offset(0, 4),
                      blurRadius: 12,
                    ),
                  ],
                ),
                child: const Center(
                  child: Text(
                    'More Info',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建规格行
  Widget _buildSpecRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withValues(alpha: 0.6),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
